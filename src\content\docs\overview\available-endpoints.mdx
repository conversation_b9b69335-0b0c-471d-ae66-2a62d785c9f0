---
title: Inflow Inventory API Endpoints
description: List of all available endpoints for the inFlow API
---

import { Tabs, TabItem } from '@astrojs/starlight/components';

## Adjustment Reason

### Get adjustment reasons
<Tabs>
  <TabItem label="Endpoint">
    <Code>
      GET /{companyId}/adjustment-reasons/{adjustmentReasonId}
    </Code>
  </TabItem>
  <TabItem label="URL">
    <Code>
      GET /{companyId}/adjustment-reasons/{adjustmentReasonId}
    </Code>
  </TabItem>
</Tabs>

### List adjustment reasons
- **GET** `/{companyId}/adjustment-reasons`

## Categories

### Get a category
- **GET** `/{companyId}/categories/{categoryId}`

### List categories
- **GET** `/{companyId}/categories`

## Currency

### List currencies
- **GET** `/{companyId}/currencies`

### Get a currency
- **GET** `/{companyId}/currencies/{currencyId}`

## Customer

### Get a customer
- **GET** `/{companyId}/customers/{customerId}`

### List customers
- **GET** `/{companyId}/customers`

### Insert or update a customer
- **PUT** `/{companyId}/customers`

## Custom Field Definitions

### Get custom field definitions
- **GET** `/{companyId}/custom-field-definitions`

### Insert or update custom field definition
- **PUT** `/{companyId}/custom-field-definitions`

### Get all dropdown custom field options for an entity
- **GET** `/{companyId}/custom-field-dropdown-options/{entityType}`

### Set custom field dropdown options
- **PUT** `/{companyId}/custom-field-dropdown-options`

## Custom Fields

### Get custom field labels
- **GET** `/{companyId}/custom-fields`

### Insert or Update custom field labels
- **PUT** `/{companyId}/custom-fields`

## Location

### Get a location
- **GET** `/{companyId}/locations/{locationId}`

### List locations
- **GET** `/{companyId}/locations`

### Get suggested sublocations
- **GET** `/{companyId}/locations/{locationId}/suggested-sublocations`

### Get suggested sublocations for a given product and location
- **GET** `/{companyId}/locations/{locationId}/products/{productId}/suggested-sublocations`

## Manufacturing Order

### Get a manufacture order
- **GET** `/{companyId}/manufacturing-orders/{manufacturingOrderId}`

### Insert or update a manufacture order
- **PUT** `/{companyId}/manufacturing-orders`

### List manufacture orders
- **GET** `/{companyId}/manufacturing-orders`

### Get manufacturing order operation status
- **GET** `/{companyId}/manufacturing-orders/{manufacturingOrderId}/operation-status/{operationId}`

### Insert or update manufacturing order operation status
- **PUT** `/{companyId}/manufacturing-orders/{manufacturingOrderGuid}/operation-statuses`

## Operation Type

### Get an Operation Type
- **GET** `/{companyId}/operation-types/{operationTypeId}`

### List Operation Types
- **GET** `/{companyId}/operation-types`

## Payment Terms

### Get payment terms
- **GET** `/{companyId}/payment-terms/{paymentTermsId}`

### List payment terms
- **GET** `/{companyId}/payment-terms`

## Pricing Scheme

### Get a pricing scheme
- **GET** `/{companyId}/pricing-schemes/{pricingSchemeId}`

### List pricing schemes
- **GET** `/{companyId}/pricing-schemes`

## Product

### Get a product
- **GET** `/{companyId}/products/{productId}`

### List products
- **GET** `/{companyId}/products`

### Insert or update product
- **PUT** `/{companyId}/products`

### Get product inventory summary
- **GET** `/{companyId}/products/{productId}/inventory-summary`

### Get multiple product inventory summaries
- **POST** `/{companyId}/products/summary`

## Product Cost Adjustment

### Get a product cost adjustment
- **GET** `/{companyId}/product-cost-adjustments/{productCostAdjustmentId}`

### List product cost adjustments
- **GET** `/{companyId}/product-cost-adjustments`

### Insert or update product cost adjustment
- **PUT** `/{companyId}/product-cost-adjustments`

## Purchase Order

### List purchase orders
- **GET** `/{companyId}/purchase-orders`

### Insert or update purchase order
- **PUT** `/{companyId}/purchase-orders`

### Get a purchase order
- **GET** `/{companyId}/purchase-orders/{purchaseOrderId}`

## Sales Order

### Get a sales order
- **GET** `/{companyId}/sales-orders/{salesOrderId}`

### List sales orders
- **GET** `/{companyId}/sales-orders`

### Insert or update sales order
- **PUT** `/{companyId}/sales-orders`

## Stock Adjustment

### Get a stock adjustment
- **GET** `/{companyId}/stock-adjustments/{stockAdjustmentId}`

### List stock adjustments
- **GET** `/{companyId}/stock-adjustments`

### Insert or update a stock adjustment
- **PUT** `/{companyId}/stock-adjustments`

## Stock Count

### Get a stock count
- **GET** `/{companyId}/stock-counts/{stockCountId}`

### List stock counts
- **GET** `/{companyId}/stock-counts`

### Insert or update a stock count
- **PUT** `/{companyId}/stock-counts`

### Delete a count sheet
- **DELETE** `/{companyId}/stock-counts/{stockCountId}/count-sheets/{countSheetId}`

### Get a count sheet
- **GET** `/{companyId}/count-sheets/{countSheetId}`

### Insert or update a count sheet
- **PUT** `/{companyId}/count-sheets`

## Stockroom Scan

### List stockroom scans
- **GET** `/{companyId}/stockroom-scans`

### Insert or update a stockroom scan
- **PUT** `/{companyId}/stockroom-scans`

## Stockroom User

### Get stockroom users
- **GET** `/{companyId}/stockroom-users/{stockroomUserId}`

### List stockroom users
- **GET** `/{companyId}/stockroom-users`

## Stock Transfer

### Get a stock transfer
- **GET** `/{companyId}/stock-transfers/{stockTransferId}`

### List stock transfers
- **GET** `/{companyId}/stock-transfers`

### Insert or update a stock transfer
- **PUT** `/{companyId}/stock-transfers`

## Tax Code

### Get a tax code
- **GET** `/{companyId}/tax-codes/{taxCodeId}`

### List tax codes
- **GET** `/{companyId}/tax-codes`

## Taxing Scheme

### Get a taxing scheme
- **GET** `/{companyId}/taxing-schemes/{taxingSchemeId}`

### List taxing schemes
- **GET** `/{companyId}/taxing-schemes`

### Insert or update taxing scheme
- **PUT** `/{companyId}/taxing-schemes`

## Team Member

### List team members
- **GET** `/{companyId}/team-members`

## Vendor

### Get a vendor
- **GET** `/{companyId}/vendors/{vendorId}`

### List vendors
- **GET** `/{companyId}/vendors`

### Insert or update a vendor
- **PUT** `/{companyId}/vendors`

## Web Hooks

### List all subscribed webhooks
- **GET** `/{companyId}/webhooks`

### Subscribe to a webhook
- **PUT** `/{companyId}/webhooks`

### Get a webhook subscription
- **GET** `/{companyId}/webhooks/{webHookId}`

### Unsubscribe from a webhook
- **DELETE** `/{companyId}/webhooks/{webHookId}`

